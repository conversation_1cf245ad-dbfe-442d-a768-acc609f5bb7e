<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Take It Easy - Teenage Problems & Giving Advice</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 10px;
        }

        .header p {
            color: white;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .nav-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        }

        .lesson-section {
            display: none;
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .lesson-section.active {
            display: block;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section-title {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid #4ecdc4;
            padding-bottom: 10px;
        }

        .problem-card {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .advice-card {
            background: linear-gradient(135deg, #a8e6cf, #88d8c0);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .sentence-structure {
            background: linear-gradient(135deg, #e17055, #fdcb6e);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .example-box {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }

        .interactive-exercise {
            background: linear-gradient(135deg, #fd79a8, #fdcb6e);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .exercise-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .exercise-btn:hover {
            background: #4ecdc4;
            transform: scale(1.05);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 5px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #4ecdc4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .emoji {
            font-size: 1.5em;
            margin: 0 5px;
        }

        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .scoreboard {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            min-width: 200px;
        }

        .team-score {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            border-radius: 5px;
        }

        .team1 { background: #ff6b6b; color: white; }
        .team2 { background: #4ecdc4; color: white; }
        .team3 { background: #667eea; color: white; }
        .team4 { background: #fdcb6e; color: #333; }

        @media (max-width: 768px) {
            .scoreboard {
                position: relative;
                top: 0;
                right: 0;
                margin-bottom: 20px;
            }
            
            .navigation {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Take It Easy <span class="emoji">😌</span></h1>
            <p>Learning to Give Advice About Teenage Problems</p>
        </div>

        <div class="scoreboard">
            <h3>Team Scores</h3>
            <div class="team-score team1">
                <span>Team 1</span>
                <span id="score1">0</span>
            </div>
            <div class="team-score team2">
                <span>Team 2</span>
                <span id="score2">0</span>
            </div>
            <div class="team-score team3">
                <span>Team 3</span>
                <span id="score3">0</span>
            </div>
            <div class="team-score team4">
                <span>Team 4</span>
                <span id="score4">0</span>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="navigation">
            <button class="nav-btn active" onclick="showSection('problems')">Teenage Problems</button>
            <button class="nav-btn" onclick="showSection('sentence-structure')">Sentence Structure</button>
            <button class="nav-btn" onclick="showSection('giving-advice')">Giving Advice</button>
            <button class="nav-btn" onclick="showSection('practice')">Practice</button>
            <button class="nav-btn" onclick="showSection('games')">Games</button>
            <button class="nav-btn" onclick="showSection('family-feud')">Family Feud</button>
        </div>

        <!-- Teenage Problems Section -->
        <div id="problems" class="lesson-section active">
            <h2 class="section-title">Common Teenage Problems <span class="emoji">😟</span></h2>
            
            <div class="problem-card">
                <h3><span class="emoji">📚</span> Taking Exams</h3>
                <p>Many teenagers feel stressed about tests and exams. This is very common!</p>
                <div class="example-box">
                    <strong>Example:</strong> "I have five exams next week and I feel really nervous."
                </div>
            </div>

            <div class="problem-card">
                <h3><span class="emoji">🏫</span> Changing Schools</h3>
                <p>Moving to a new school can be scary and lonely.</p>
                <div class="example-box">
                    <strong>Example:</strong> "I don't know anyone at my new school."
                </div>
            </div>

            <div class="problem-card">
                <h3><span class="emoji">😢</span> Being Bullied</h3>
                <p>Some students are mean to others. This makes people feel sad and scared.</p>
                <div class="example-box">
                    <strong>Example:</strong> "Older students say mean things to me every day."
                </div>
            </div>

            <div class="problem-card">
                <h3><span class="emoji">👨‍👩‍👧</span> Arguing with Parents</h3>
                <p>Sometimes teenagers and parents don't agree about things.</p>
                <div class="example-box">
                    <strong>Example:</strong> "My parents don't understand me."
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Quick Check! <span class="emoji">✅</span></h3>
                <p>Click on the problems you have experienced:</p>
                <button class="exercise-btn" onclick="selectProblem(this)">Taking Exams</button>
                <button class="exercise-btn" onclick="selectProblem(this)">Changing Schools</button>
                <button class="exercise-btn" onclick="selectProblem(this)">Being Bullied</button>
                <button class="exercise-btn" onclick="selectProblem(this)">Arguing with Parents</button>
                <button class="exercise-btn" onclick="selectProblem(this)">Moving House</button>
                <button class="exercise-btn" onclick="selectProblem(this)">Losing Friends</button>
            </div>
        </div>

        <!-- Sentence Structure Section -->
        <div id="sentence-structure" class="lesson-section">
            <h2 class="section-title">How to Structure Advice Sentences <span class="emoji">📝</span></h2>

            <div class="sentence-structure">
                <h3><span class="emoji">💡</span> Using "Should" and "Shouldn't"</h3>
                <div class="example-box">
                    <strong>Pattern:</strong> You <span class="highlight">should</span> + base verb<br>
                    <strong>Example:</strong> "You should talk to your teacher."<br>
                    <strong>Negative:</strong> "You shouldn't ignore the problem."
                </div>
            </div>

            <div class="sentence-structure">
                <h3><span class="emoji">🎯</span> Giving Direct Advice (Imperatives)</h3>
                <div class="example-box">
                    <strong>Pattern:</strong> Base verb + rest of sentence<br>
                    <strong>Example:</strong> "Talk to your parents about it."<br>
                    <strong>Polite:</strong> "Try to make new friends in your class."
                </div>
            </div>

            <div class="sentence-structure">
                <h3><span class="emoji">🤔</span> Asking for Advice</h3>
                <div class="example-box">
                    <strong>Pattern:</strong> Should I + base verb?<br>
                    <strong>Example:</strong> "Should I speak to a counselor?"<br>
                    <strong>Alternative:</strong> "What should I do about this problem?"
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Sentence Builder <span class="emoji">🔧</span></h3>
                <p>Click the words in order to make good advice sentences:</p>
                <div id="sentenceBuilder">
                    <div class="word-bank">
                        <button class="exercise-btn" onclick="addWord(this)">You</button>
                        <button class="exercise-btn" onclick="addWord(this)">should</button>
                        <button class="exercise-btn" onclick="addWord(this)">talk</button>
                        <button class="exercise-btn" onclick="addWord(this)">to</button>
                        <button class="exercise-btn" onclick="addWord(this)">your</button>
                        <button class="exercise-btn" onclick="addWord(this)">teacher</button>
                    </div>
                    <div id="builtSentence" style="min-height: 40px; border: 2px dashed #ccc; padding: 10px; margin: 10px 0;"></div>
                    <button onclick="clearSentence()" style="background: #ff6b6b; color: white; border: none; padding: 8px 16px; border-radius: 15px;">Clear</button>
                </div>
            </div>
        </div>

        <!-- Giving Advice Section -->
        <div id="giving-advice" class="lesson-section">
            <h2 class="section-title">How to Give Good Advice <span class="emoji">🤝</span></h2>

            <div class="advice-card">
                <h3><span class="emoji">👂</span> Step 1: Listen and Understand</h3>
                <p>Before giving advice, make sure you understand the problem.</p>
                <div class="example-box">
                    <strong>Good responses:</strong><br>
                    • "I understand how you feel."<br>
                    • "That sounds really difficult."<br>
                    • "I'm sorry you're going through this."
                </div>
            </div>

            <div class="advice-card">
                <h3><span class="emoji">💭</span> Step 2: Give Helpful Suggestions</h3>
                <p>Offer practical solutions that can really help.</p>
                <div class="example-box">
                    <strong>For exam stress:</strong><br>
                    • "You should make notes as you read."<br>
                    • "Try to get enough sleep before exams."<br>
                    • "Don't study all night before the test."
                </div>
            </div>

            <div class="advice-card">
                <h3><span class="emoji">🌟</span> Step 3: Be Encouraging</h3>
                <p>Help the person feel better and more confident.</p>
                <div class="example-box">
                    <strong>Encouraging phrases:</strong><br>
                    • "You can do this!"<br>
                    • "Things will get better."<br>
                    • "You're stronger than you think."
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Advice Matching <span class="emoji">🎯</span></h3>
                <p>Match the problem with the best advice:</p>
                <div id="adviceMatching">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4>Problems:</h4>
                            <button class="exercise-btn problem-btn" data-problem="exam">I'm stressed about exams</button>
                            <button class="exercise-btn problem-btn" data-problem="bully">Someone is bullying me</button>
                            <button class="exercise-btn problem-btn" data-problem="friends">I have no friends at school</button>
                        </div>
                        <div>
                            <h4>Advice:</h4>
                            <button class="exercise-btn advice-btn" data-advice="exam">Make study notes and get enough sleep</button>
                            <button class="exercise-btn advice-btn" data-advice="bully">Tell a teacher or parent about it</button>
                            <button class="exercise-btn advice-btn" data-advice="friends">Join clubs and be friendly to classmates</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Practice Section -->
        <div id="practice" class="lesson-section">
            <h2 class="section-title">Practice Giving Advice <span class="emoji">💪</span></h2>

            <div class="interactive-exercise">
                <h3>Penny's Email <span class="emoji">📧</span></h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <p><strong>From:</strong> Penny</p>
                    <p><strong>To:</strong> Stella</p>
                    <p><strong>Subject:</strong> Help! School problems</p>
                    <br>
                    <p>Hi Stella,</p>
                    <p>High school is really tough for me. I don't know anyone and I get picked on by 12th graders. I feel lonely. Any advice?</p>
                    <p>Penny</p>
                </div>

                <h4>Help Stella write a good reply! <span class="emoji">✍️</span></h4>
                <div id="emailReply">
                    <p>Choose the best sentences for Stella's reply:</p>
                    <button class="exercise-btn" onclick="selectReply(this, true)">I'm sorry to hear you're having trouble at school.</button>
                    <button class="exercise-btn" onclick="selectReply(this, false)">That's not my problem.</button>
                    <button class="exercise-btn" onclick="selectReply(this, true)">You should try to ignore the bullies.</button>
                    <button class="exercise-btn" onclick="selectReply(this, false)">Just fight them back.</button>
                    <button class="exercise-btn" onclick="selectReply(this, true)">Try to make friends in your own class.</button>
                    <button class="exercise-btn" onclick="selectReply(this, true)">Talk to a teacher about the bullying.</button>
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Complete the Advice <span class="emoji">🔤</span></h3>
                <p>Fill in the missing words:</p>
                <div style="background: white; padding: 15px; border-radius: 8px;">
                    <p>1. You _______ talk to your parents about it. (should/would)</p>
                    <p>2. _______ to make new friends in your class. (Try/Trying)</p>
                    <p>3. You _______ ignore the problem. (should/shouldn't)</p>
                    <p>4. _______ I speak to a counselor? (Should/Would)</p>
                </div>
                <button onclick="checkAnswers()" class="exercise-btn" style="margin-top: 10px;">Check Answers</button>
                <div id="answerFeedback" style="margin-top: 10px;"></div>
            </div>
        </div>

        <!-- Games Section -->
        <div id="games" class="lesson-section">
            <h2 class="section-title">Fun Learning Games <span class="emoji">🎮</span></h2>

            <div class="interactive-exercise">
                <h3>Problem Scramble <span class="emoji">🔀</span></h3>
                <p>Unscramble these teenage problems:</p>
                <div id="scrambleGame">
                    <div class="scramble-item">
                        <p><strong>BUELILD</strong> = <input type="text" id="scramble1" placeholder="Your answer">
                        <button onclick="checkScramble(1, 'bullied')">Check</button></p>
                    </div>
                    <div class="scramble-item">
                        <p><strong>MAXE SSERTS</strong> = <input type="text" id="scramble2" placeholder="Your answer">
                        <button onclick="checkScramble(2, 'exam stress')">Check</button></p>
                    </div>
                    <div class="scramble-item">
                        <p><strong>GNIVOM SUEOH</strong> = <input type="text" id="scramble3" placeholder="Your answer">
                        <button onclick="checkScramble(3, 'moving house')">Check</button></p>
                    </div>
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Advice Wheel <span class="emoji">🎡</span></h3>
                <p>Spin the wheel to get a random problem, then give advice!</p>
                <div style="text-align: center;">
                    <div id="adviceWheel" style="width: 200px; height: 200px; border: 5px solid #667eea; border-radius: 50%; margin: 20px auto; display: flex; align-items: center; justify-content: center; background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #667eea, #fdcb6e); font-size: 18px; font-weight: bold; color: white;">
                        Click to Spin!
                    </div>
                    <button onclick="spinWheel()" class="exercise-btn" style="font-size: 18px; padding: 15px 30px;">Spin the Wheel!</button>
                    <div id="wheelResult" style="margin-top: 20px; font-size: 18px; font-weight: bold;"></div>
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Family Feud Challenge <span class="emoji">🏆</span></h3>
                <p>Ready for the ultimate challenge? Test your knowledge about teenage problems!</p>
                <div style="text-align: center; margin: 20px 0;">
                    <button onclick="showSection('family-feud')" class="exercise-btn" style="font-size: 20px; padding: 20px 40px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4);">
                        🎯 Play Family Feud!
                    </button>
                </div>
            </div>

            <div class="interactive-exercise">
                <h3>Quick Quiz <span class="emoji">⚡</span></h3>
                <div id="quickQuiz">
                    <div class="quiz-question">
                        <p><strong>Question 1:</strong> What should you do if someone is bullying you?</p>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Fight back immediately</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, true)">Tell a teacher or parent</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Ignore it completely</button>
                    </div>
                    <div class="quiz-question" style="display: none;">
                        <p><strong>Question 2:</strong> How can you reduce exam stress?</p>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Study all night before the exam</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, true)">Make notes and get enough sleep</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Don't study at all</button>
                    </div>
                    <div class="quiz-question" style="display: none;">
                        <p><strong>Question 3:</strong> What's a good way to make friends at a new school?</p>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Wait for others to talk to you first</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, true)">Join clubs and be friendly</button>
                        <button class="exercise-btn" onclick="selectQuizAnswer(this, false)">Stay alone and don't talk to anyone</button>
                    </div>
                </div>
                <div id="quizScore" style="margin-top: 20px; font-size: 18px; font-weight: bold;"></div>
            </div>
        </div>

        <!-- Family Feud Section -->
        <div id="family-feud" class="lesson-section">
            <h2 class="section-title">Family Feud: Teenage Problems <span class="emoji">🎯</span></h2>

            <div class="feud-container">
                <div class="feud-header">
                    <h1>Take It Easy - Family Feud!</h1>
                    <p>Find the most common teenage problems and advice!</p>
                    <p><strong>Find answers worth: 5 • 15 • 20 • 25 • 30 points</strong></p>
                </div>

                <div class="feud-instructions">
                    <h3>👋 How to Play</h3>
                    <p>1. Pick a topic about teenage problems</p>
                    <p>2. Click on the boxes to reveal common answers</p>
                    <p>3. Try to find all the answers to win!</p>
                </div>

                <div class="feud-category-selector" id="feudCategorySelector">
                    <!-- Category buttons will be generated here -->
                </div>

                <div class="feud-question-display" id="feudQuestionDisplay">
                    Select a category to start playing!
                </div>

                <div class="feud-strike-system" id="feudStrikeSystem" style="display: none;">
                    <div>Strikes:</div>
                    <div class="feud-strike" id="feudStrike1">❌</div>
                    <div class="feud-strike" id="feudStrike2">❌</div>
                    <div class="feud-strike" id="feudStrike3">❌</div>
                </div>

                <div class="feud-answers-board" id="feudAnswersBoard">
                    <!-- Answer slots will be generated here -->
                </div>

                <div class="feud-total-score" id="feudTotalScore">
                    Total Score: 0 points
                </div>

                <div class="feud-controls">
                    <button class="feud-control-btn feud-reveal-all-btn" onclick="feudRevealAll()">⚡ Reveal All</button>
                    <button class="feud-control-btn feud-reset-btn" onclick="feudResetRound()">🔄 New Round</button>
                    <button class="feud-control-btn" onclick="showSection('games')">✅ Back to Games</button>
                </div>

                <div class="feud-completion-message" id="feudCompletionMessage">
                    <!-- Completion message will appear here -->
                </div>
            </div>
        </div>

    <style>
        /* Family Feud Specific Styles */
        .feud-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .feud-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border-radius: 15px;
            border: 3px solid #FFF;
        }

        .feud-question-display {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            padding: 25px;
            background: #E6F3FF;
            border: 3px dashed #667eea;
            border-radius: 15px;
            color: #333;
        }

        .feud-answers-board {
            background: #F0F8FF;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 3px solid #667eea;
        }

        .feud-answer-slot {
            background: #FFF;
            border: 3px solid #ff6b6b;
            border-radius: 12px;
            margin: 10px 0;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .feud-answer-slot:hover {
            background: #FFE4E1;
            transform: scale(1.02);
        }

        .feud-answer-slot.revealed {
            background: linear-gradient(45deg, #4ecdc4, #45b7aa);
            border-color: #3CB371;
            color: #333;
            cursor: default;
        }

        .feud-answer-slot.revealed:hover {
            background: linear-gradient(45deg, #4ecdc4, #45b7aa);
        }

        .feud-answer-number {
            font-size: 24px;
            font-weight: bold;
            color: #ecf0f1;
            width: 40px;
        }

        .feud-answer-text {
            flex-grow: 1;
            font-size: 20px;
            font-weight: bold;
            color: #ecf0f1;
            text-align: center;
            letter-spacing: 2px;
        }

        .feud-answer-slot.revealed .feud-answer-text {
            color: white;
            letter-spacing: normal;
        }

        .feud-answer-points {
            font-size: 24px;
            font-weight: bold;
            color: #f39c12;
            width: 60px;
            text-align: right;
        }

        .feud-answer-slot.revealed .feud-answer-points {
            color: #fff;
        }

        .feud-hidden-answer {
            visibility: hidden;
        }

        .feud-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .feud-control-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            border: 2px solid #FFF;
        }

        .feud-control-btn:hover {
            background: #4ecdc4;
            transform: scale(1.05);
        }

        .feud-reveal-all-btn {
            background: #28a745;
        }

        .feud-reveal-all-btn:hover {
            background: #218838;
        }

        .feud-reset-btn {
            background: #6c757d;
        }

        .feud-reset-btn:hover {
            background: #545b62;
        }

        .feud-category-selector {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .feud-category-btn {
            background: #FFF;
            border: 3px solid #667eea;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
            color: #333;
        }

        .feud-category-btn:hover {
            background: #E6F3FF;
            border-color: #ff6b6b;
            transform: scale(1.05);
        }

        .feud-category-btn.active {
            background: #ff6b6b;
            color: white;
            border-color: #4ecdc4;
        }

        .feud-total-score {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #ffeaa7, #fdcb6e);
            border-radius: 15px;
            color: #2d3436;
        }

        .feud-instructions {
            background: #FFF5EE;
            border: 3px dashed #ff6b6b;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .feud-instructions h3 {
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .feud-instructions p {
            margin: 10px 0;
            font-size: 18px;
            color: #333;
        }

        .feud-strike-system {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }

        .feud-strike {
            font-size: 48px;
            color: #e74c3c;
            opacity: 0.3;
            transition: opacity 0.3s ease;
        }

        .feud-strike.active {
            opacity: 1;
            animation: pulse 1s infinite;
        }

        .feud-completion-message {
            text-align: center;
            padding: 30px;
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-radius: 15px;
            margin-top: 20px;
            display: none;
            font-size: 20px;
            font-weight: bold;
        }
    </style>

    <script>
        // Family Feud Game Data for Teenage Problems
        const feudGameData = {
            'Common Teenage Problems': {
                question: 'What problems do teenagers often face?',
                answers: [
                    { text: 'Taking exams', points: 30 },
                    { text: 'Being bullied', points: 25 },
                    { text: 'Arguing with parents', points: 20 },
                    { text: 'Changing schools', points: 15 },
                    { text: 'Moving house', points: 5 }
                ]
            },
            'Good Advice for Stress': {
                question: 'What should you do when feeling stressed?',
                answers: [
                    { text: 'Talk to someone', points: 30 },
                    { text: 'Take deep breaths', points: 25 },
                    { text: 'Get enough sleep', points: 20 },
                    { text: 'Exercise regularly', points: 15 },
                    { text: 'Listen to music', points: 5 }
                ]
            },
            'Making Friends': {
                question: 'How can you make new friends at school?',
                answers: [
                    { text: 'Join clubs', points: 30 },
                    { text: 'Be friendly', points: 25 },
                    { text: 'Help others', points: 20 },
                    { text: 'Share interests', points: 15 },
                    { text: 'Smile often', points: 5 }
                ]
            },
            'Dealing with Bullies': {
                question: 'What should you do if someone bullies you?',
                answers: [
                    { text: 'Tell a teacher', points: 30 },
                    { text: 'Tell your parents', points: 25 },
                    { text: 'Walk away', points: 20 },
                    { text: 'Stay with friends', points: 15 },
                    { text: 'Be confident', points: 5 }
                ]
            },
            'Study Tips': {
                question: 'How can you study better for exams?',
                answers: [
                    { text: 'Make study notes', points: 30 },
                    { text: 'Study with friends', points: 25 },
                    { text: 'Take breaks', points: 20 },
                    { text: 'Ask for help', points: 15 },
                    { text: 'Start early', points: 5 }
                ]
            }
        };

        // Family Feud Variables
        let feudCurrentCategory = '';
        let feudCurrentAnswers = [];
        let feudRevealedCount = 0;
        let feudTotalScore = 0;
        let feudStrikes = 0;

        // Global variables
        let currentSection = 'problems';
        let teamScores = [0, 0, 0, 0];
        let currentQuizQuestion = 0;
        let quizScore = 0;
        let builtSentence = [];
        let selectedProblem = null;
        let selectedAdvice = null;

        // Family Feud Functions
        function initializeFamilyFeud() {
            createFamilyFeudCategorySelector();
        }

        function createFamilyFeudCategorySelector() {
            const selector = document.getElementById('feudCategorySelector');
            selector.innerHTML = '';

            Object.keys(feudGameData).forEach(category => {
                const btn = document.createElement('div');
                btn.className = 'feud-category-btn';
                btn.textContent = category;
                btn.onclick = () => selectFamilyFeudCategory(category);
                selector.appendChild(btn);
            });
        }

        function selectFamilyFeudCategory(category) {
            feudCurrentCategory = category;
            feudCurrentAnswers = [...feudGameData[category].answers];
            feudRevealedCount = 0;
            feudStrikes = 0;

            // Update category button states
            document.querySelectorAll('.feud-category-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent === category) {
                    btn.classList.add('active');
                }
            });

            // Show question
            document.getElementById('feudQuestionDisplay').textContent = feudGameData[category].question;

            // Show strike system
            document.getElementById('feudStrikeSystem').style.display = 'flex';
            updateFamilyFeudStrikes();

            // Create answer board
            createFamilyFeudAnswerBoard();

            // Hide completion message
            document.getElementById('feudCompletionMessage').style.display = 'none';
        }

        function createFamilyFeudAnswerBoard() {
            const board = document.getElementById('feudAnswersBoard');
            board.innerHTML = '';

            feudCurrentAnswers.forEach((answer, index) => {
                const slot = document.createElement('div');
                slot.className = 'feud-answer-slot';
                slot.onclick = () => revealFamilyFeudAnswer(index);

                slot.innerHTML = `
                    <div class="feud-answer-number">${index + 1}</div>
                    <div class="feud-answer-text feud-hidden-answer">${answer.text}</div>
                    <div class="feud-answer-points feud-hidden-answer">${answer.points}</div>
                `;

                board.appendChild(slot);
            });
        }

        function revealFamilyFeudAnswer(index) {
            const slots = document.querySelectorAll('.feud-answer-slot');
            const slot = slots[index];

            if (!slot.classList.contains('revealed')) {
                slot.classList.add('revealed');
                const answerText = slot.querySelector('.feud-answer-text');
                const answerPoints = slot.querySelector('.feud-answer-points');
                answerText.classList.remove('feud-hidden-answer');
                answerPoints.classList.remove('feud-hidden-answer');

                feudTotalScore += feudCurrentAnswers[index].points;
                updateFamilyFeudTotalScore();

                feudRevealedCount++;

                // Check if all answers revealed
                if (feudRevealedCount === feudCurrentAnswers.length) {
                    setTimeout(() => {
                        showFamilyFeudCompletionMessage();
                    }, 1000);
                }
            }
        }

        function updateFamilyFeudTotalScore() {
            document.getElementById('feudTotalScore').textContent = `Total Score: ${feudTotalScore} points`;
        }

        function updateFamilyFeudStrikes() {
            for (let i = 1; i <= 3; i++) {
                const strike = document.getElementById(`feudStrike${i}`);
                if (i <= feudStrikes) {
                    strike.classList.add('active');
                } else {
                    strike.classList.remove('active');
                }
            }
        }

        function feudRevealAll() {
            const slots = document.querySelectorAll('.feud-answer-slot');
            let delay = 0;

            slots.forEach((slot, index) => {
                if (!slot.classList.contains('revealed')) {
                    setTimeout(() => {
                        slot.classList.add('revealed');
                        const answerText = slot.querySelector('.feud-answer-text');
                        const answerPoints = slot.querySelector('.feud-answer-points');
                        answerText.classList.remove('feud-hidden-answer');
                        answerPoints.classList.remove('feud-hidden-answer');

                        feudTotalScore += feudCurrentAnswers[index].points;
                        updateFamilyFeudTotalScore();
                    }, delay);
                    delay += 500;
                }
            });

            setTimeout(() => {
                showFamilyFeudCompletionMessage();
            }, delay + 500);
        }

        function showFamilyFeudCompletionMessage() {
            const message = document.getElementById('feudCompletionMessage');
            let performanceMsg = '';

            if (feudTotalScore >= 90) {
                performanceMsg = '🌟 Amazing! You know so much about teenage problems!';
            } else if (feudTotalScore >= 70) {
                performanceMsg = '👍 Great job! You understand teenage challenges well!';
            } else if (feudTotalScore >= 50) {
                performanceMsg = '😊 Well done! Keep learning about giving advice!';
            } else {
                performanceMsg = '💪 Good try! Practice more to become an advice expert!';
            }

            message.innerHTML = `
                <h2>🎉 Round Complete!</h2>
                <div style="font-size: 36px; margin: 20px 0;">You scored ${feudTotalScore} points!</div>
                <p>${performanceMsg}</p>
                <button class="feud-control-btn" onclick="feudResetRound()" style="margin-top: 15px;">
                    🎮 Play Again
                </button>
            `;

            message.style.display = 'block';
        }

        function feudResetRound() {
            feudTotalScore = 0;
            feudRevealedCount = 0;
            feudStrikes = 0;
            feudCurrentCategory = '';

            updateFamilyFeudTotalScore();

            // Reset category selector
            document.querySelectorAll('.feud-category-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Reset displays
            document.getElementById('feudQuestionDisplay').textContent = 'Select a category to start playing!';
            document.getElementById('feudAnswersBoard').innerHTML = '';
            document.getElementById('feudStrikeSystem').style.display = 'none';
            document.getElementById('feudCompletionMessage').style.display = 'none';
        }

        // Navigation function
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.lesson-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');

            currentSection = sectionId;
            updateProgress();

            // Initialize Family Feud if that section is selected
            if (sectionId === 'family-feud') {
                initializeFamilyFeud();
            }
        }

        // Update progress bar
        function updateProgress() {
            const sections = ['problems', 'sentence-structure', 'giving-advice', 'practice', 'games', 'family-feud'];
            const currentIndex = sections.indexOf(currentSection);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // Problem selection function
        function selectProblem(button) {
            button.style.background = button.style.background === 'rgb(76, 220, 196)' ? '#667eea' : '#4ecdc4';
            addTeamScore(1, 5);
        }

        // Sentence builder functions
        function addWord(button) {
            const word = button.textContent;
            builtSentence.push(word);
            updateBuiltSentence();
            button.style.opacity = '0.5';
            button.disabled = true;
        }

        function updateBuiltSentence() {
            document.getElementById('builtSentence').textContent = builtSentence.join(' ');
        }

        function clearSentence() {
            builtSentence = [];
            updateBuiltSentence();
            document.querySelectorAll('#sentenceBuilder .exercise-btn').forEach(btn => {
                btn.style.opacity = '1';
                btn.disabled = false;
            });
        }

        // Advice matching functions
        function selectProblemBtn(button) {
            document.querySelectorAll('.problem-btn').forEach(btn => btn.style.background = '#667eea');
            button.style.background = '#ff6b6b';
            selectedProblem = button.dataset.problem;
            checkAdviceMatch();
        }

        function selectAdviceBtn(button) {
            document.querySelectorAll('.advice-btn').forEach(btn => btn.style.background = '#667eea');
            button.style.background = '#4ecdc4';
            selectedAdvice = button.dataset.advice;
            checkAdviceMatch();
        }

        function checkAdviceMatch() {
            if (selectedProblem && selectedAdvice) {
                if (selectedProblem === selectedAdvice) {
                    alert('Correct match! 🎉');
                    addTeamScore(2, 10);
                } else {
                    alert('Try again! 🤔');
                }
                selectedProblem = null;
                selectedAdvice = null;
                document.querySelectorAll('.problem-btn, .advice-btn').forEach(btn => btn.style.background = '#667eea');
            }
        }

        // Email reply function
        function selectReply(button, isCorrect) {
            if (isCorrect) {
                button.style.background = '#4ecdc4';
                addTeamScore(3, 5);
                setTimeout(() => {
                    alert('Good choice! That\'s helpful advice. 👍');
                }, 100);
            } else {
                button.style.background = '#ff6b6b';
                setTimeout(() => {
                    alert('That\'s not very helpful. Try to be more supportive! 😊');
                }, 100);
            }
        }

        // Check answers function
        function checkAnswers() {
            const answers = ['should', 'Try', 'shouldn\'t', 'Should'];
            const feedback = document.getElementById('answerFeedback');
            feedback.innerHTML = '<h4>Correct Answers:</h4><p>1. should | 2. Try | 3. shouldn\'t | 4. Should</p>';
            addTeamScore(4, 15);
        }

        // Scramble game function
        function checkScramble(questionNum, correctAnswer) {
            const userAnswer = document.getElementById('scramble' + questionNum).value.toLowerCase();
            if (userAnswer === correctAnswer.toLowerCase()) {
                alert('Correct! 🎉');
                addTeamScore(1, 10);
                document.getElementById('scramble' + questionNum).style.background = '#4ecdc4';
            } else {
                alert('Try again! 🤔');
                document.getElementById('scramble' + questionNum).style.background = '#ff6b6b';
            }
        }

        // Wheel spinning function
        function spinWheel() {
            const problems = [
                'Taking Exams', 'Changing Schools', 'Being Bullied',
                'Arguing with Parents', 'Moving House', 'Losing Friends'
            ];
            const randomProblem = problems[Math.floor(Math.random() * problems.length)];

            document.getElementById('adviceWheel').style.transform = 'rotate(' + (Math.random() * 360 + 720) + 'deg)';
            document.getElementById('adviceWheel').style.transition = 'transform 2s ease-out';

            setTimeout(() => {
                document.getElementById('wheelResult').innerHTML =
                    '<strong>Problem:</strong> ' + randomProblem + '<br><strong>Now give advice!</strong>';
                addTeamScore(Math.floor(Math.random() * 4) + 1, 5);
            }, 2000);
        }

        // Quiz functions
        function selectQuizAnswer(button, isCorrect) {
            const allButtons = button.parentElement.querySelectorAll('.exercise-btn');
            allButtons.forEach(btn => btn.disabled = true);

            if (isCorrect) {
                button.style.background = '#4ecdc4';
                quizScore++;
                addTeamScore(Math.floor(Math.random() * 4) + 1, 10);
            } else {
                button.style.background = '#ff6b6b';
                // Show correct answer
                allButtons.forEach(btn => {
                    if (btn.onclick.toString().includes('true')) {
                        btn.style.background = '#4ecdc4';
                    }
                });
            }

            setTimeout(() => {
                currentQuizQuestion++;
                showNextQuizQuestion();
            }, 1500);
        }

        function showNextQuizQuestion() {
            const questions = document.querySelectorAll('.quiz-question');
            if (currentQuizQuestion < questions.length) {
                questions[currentQuizQuestion - 1].style.display = 'none';
                questions[currentQuizQuestion].style.display = 'block';
            } else {
                document.getElementById('quizScore').innerHTML =
                    '<strong>Quiz Complete!</strong><br>Your score: ' + quizScore + '/' + questions.length;
            }
        }

        // Team scoring system
        function addTeamScore(teamNum, points) {
            teamScores[teamNum - 1] += points;
            document.getElementById('score' + teamNum).textContent = teamScores[teamNum - 1];
        }

        // Initialize the lesson
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();

            // Add click handlers for advice matching
            document.querySelectorAll('.problem-btn').forEach(btn => {
                btn.addEventListener('click', () => selectProblemBtn(btn));
            });

            document.querySelectorAll('.advice-btn').forEach(btn => {
                btn.addEventListener('click', () => selectAdviceBtn(btn));
            });
        });
    </script>
</body>
</html>