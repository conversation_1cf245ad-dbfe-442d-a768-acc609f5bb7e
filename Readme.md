📌 Ultimate Prompt – “Teenage Problems & Giving Advice” Lesson (Copy/paste into ChatGPT / Cursor / Claude / Copilot)
Task:
Build a complete, interactive 90-minute ESL lesson web app for 10–12-year-old students at A2–B1 level.
No user inputs except teacher control (clicks/spacebar).
Fun, colorful, dynamic, visually engaging, minimal text, age-appropriate design.
HTML + CSS + JavaScript (SVG where suitable).
Each game auto-runs or advances by teacher click.
Scoreboard tracks 4 team scores across the whole lesson.

🌟 PART 1 (First 30 min – Engage → Study → Apply)
🧩 1. Warm-up Logic Puzzles (10–15 quick puzzles)
Puzzles themed around teenage problems, school life, emotions, and giving advice.

Examples:

“Unscramble the problem: BUELILD = _____” (bullied).

“Which doesn’t belong? changing schools / taking exams / winning the lottery / moving house.”

“Match the emoji to the problem: 😢 → being teased.”

SVG visuals: school lockers, exam papers, moving boxes, text bubbles.

Show Answer button, auto-shuffle each time.

🧠 2. Vocabulary Memory Game
4×4 grid (A1–D4).

Pairs: correct phrase vs scrambled/missing-word version.

Vocabulary from lesson: taking exams, changing schools, losing friends, arguing with parents, moving into a new house, gaining/losing weight, being bullied/teased at school, disagreeing with parents, having no money.

Each card labeled (A1, B3 etc.) for calling out.

Cards flip on click; correct pairs stay open, wrong ones flip back.

Shuffle button.

✏️ 3. Interactive Vocabulary Description Quiz
Multiple-choice (A–D).

Questions describe situations in simple, kid-friendly language.

Example: “You do this when you move to a new class in a different school.” (Answer: changing schools)

Example: “This happens when older students say mean things to you every day.” (Answer: being bullied)

Simple SVG visuals: sad faces, suitcases, parents talking.

Random question order each run.

📖 PART 2 (Second 30 min – Study & Apply)
⏩ 4. Speed Reading Activity
Short illustrated text from lesson content: email from Penny about school problems + reply from Stella with advice.

Example:

Penny: “High school is really tough for me. I don’t know anyone and I get picked on by 12th graders. I feel lonely. Any advice?”

Stella: “I’m sorry to hear you’re having trouble. Try to ignore the bullies and find friends in your own class…”

Manual speed control: 90–350 wpm.

Yellow highlight smoothly follows text.

After reading:

6 comprehension MCQs.

10 gap fills (missing key words from advice phrases).

Email reorder (students rearrange parts into opening, advice, closing).

💣 5. Pronunciation Minefield
6×6 grid (A1–F6).

4 teams, 3 lives each.

10 bombs hidden behind vocabulary from lesson (teenage problems/advice).

Pronounce the phrase correctly to survive.

Team colors bright & distinct.

Last team standing wins.

❌➕❓ 6. Tic-Tac-Toe Grammar Game
7×7 grid (A1–G7).

Each square has a problem phrase + symbol (+ affirmative, – negative, ? question).

Teams make a present simple or modal advice sentence:

“You should talk to your teacher.”

“You shouldn’t ignore your parents.”

“Should I speak to a school counsellor?”

Goal: 4 in a row.

Squares turn team color when claimed.

🏆 PART 3 (Final 30 min – Fun Review Competitions)
🎲 Jeopardy Game
4 teams.

Categories:

Teenage Problems (vocabulary)

What’s the Advice? (give appropriate help)

Grammar Fix (should/shouldn’t, imperatives)

Conversation Detective (fill in missing lines from Penny/Stella emails)

Coping with Exams (study tips from lesson notes)

Example:

100 pts: “This problem happens when you move away from old friends to a new place.” (Answer: changing schools)

300 pts: “He should talks to his parents. Fix it.” (Answer: should talk)

500 pts:
A: “I feel stressed about my exams.”
B: “The best thing you can do is ______.” (Answer: make notes as you read)

👨‍👩‍👧 Family Feud Game
Categories from lesson vocab/themes.

Example: “Best ways to cope with exam stress”:

Make notes as you read (5 pts)

Get enough sleep (15 pts)

Don’t study overnight before exams (20 pts)

Review notes often (25 pts)

Ask for help from a teacher (35 pts)

Teacher clicks to reveal each answer.

Points as listed; highest points = least obvious.

📌 BONUS FEATURES & LOGIC
✅ Scoreboard – always visible/toggleable. Editable team names. Add points manually if needed.
✅ Teacher panel – Next activity, Back, Shuffle, Reveal answer, Reset scores.
✅ Design – colorful, playful fonts; big buttons; consistent visuals; minimal reading.
✅ Instructions – short & kid-friendly (“Choose a square!”, “Guess now!”, “Give advice!”).
✅ Tech – HTML, CSS, vanilla JS, SVG for visuals; responsive 1-screen layout.
✅ Topics – ONLY teenage problems, school life, and giving advice from lesson screenshot.
✅ Grammar – perfect native usage, age-appropriate.